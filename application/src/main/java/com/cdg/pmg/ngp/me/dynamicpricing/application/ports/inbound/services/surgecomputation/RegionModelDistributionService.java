package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.RegionModelDistributionEntity;
import java.util.List;
import java.util.Optional;

public interface RegionModelDistributionService {

  void createOrUpdateRegionModelDistribution(RegionModelDistributionEntity entity);

  List<RegionModelDistributionEntity> getRegionModelDistribution(Long regionId);

  void deleteRegionModelDistribution(Long id);

  Optional<RegionModelDistributionEntity> getEffectiveRegionModelDistribution(Long regionId);
}
