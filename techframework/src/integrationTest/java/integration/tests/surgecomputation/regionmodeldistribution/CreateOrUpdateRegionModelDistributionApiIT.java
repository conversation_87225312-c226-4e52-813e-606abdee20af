package integration.tests.surgecomputation.regionmodeldistribution;

import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.*;
import static org.junit.jupiter.api.Assertions.*;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.*;
import integration.IntegrationTestBase;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.jdbc.Sql;
import retrofit2.Response;

/**
 * Integration tests for Region Model Distribution API operations. Tests creation, update, and
 * validation of region model distributions.
 */
@Sql(scripts = "/scripts/Cleanup.sql", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
public class CreateOrUpdateRegionModelDistributionApiIT extends IntegrationTestBase {

  private static final Long TEST_REGION_ID = 1L;
  private static final String USER_ID = "test";

  /** Tests successful creation of a region model distribution. */
  @Test
  public void testCreateRegionModelDistribution_Success() throws IOException {
    // Arrange
    List<Long> modelIds = setupModels();
    OffsetDateTime now = OffsetDateTime.now();

    Map<Long, BigDecimal> modelPercentageMap =
        Map.of(
            modelIds.get(0), BigDecimal.TEN,
            modelIds.get(1), BigDecimal.valueOf(50),
            modelIds.get(2), BigDecimal.valueOf(40));

    RegionModelDistribution distribution =
        createDistribution(
            null, TEST_REGION_ID, now.minusDays(1), now.plusDays(1), modelPercentageMap);

    // Execute
    Response<Void> response = executeCreateOrUpdateRequest(distribution);

    // Assert
    assertSuccessfulResponse(response);

    // Verify creation was successful
    Response<GetRegionModelDistributionResponse> getResponse =
        dynamicPricingServiceApi.getRegionModelDistribution(TEST_REGION_ID).execute();

    assertTrue(getResponse.isSuccessful());
    GetRegionModelDistributionResponse responseBody = getResponse.body();
    assertNotNull(responseBody);
    assertEquals(TEST_REGION_ID, responseBody.getRegionId());
    assertEquals(1, responseBody.getModelDistributionVersions().size());
    assertEquals(3, responseBody.getModelDistributionVersions().get(0).getModels().size());
    assertEquals(
        modelIds.get(0),
        responseBody.getModelDistributionVersions().get(0).getModels().get(0).getModelId());
    assertEquals(
        BigDecimal.TEN,
        responseBody.getModelDistributionVersions().get(0).getModels().get(0).getPercentage());
  }

  /** Tests successful update of an existing region model distribution. */
  @Test
  public void testUpdateRegionModelDistribution_Success() throws IOException {
    // First create a region model distribution
    List<Long> modelIds = setupModels();
    OffsetDateTime now = OffsetDateTime.now();

    Map<Long, BigDecimal> initialModelPercentageMap =
        Map.of(
            modelIds.get(0), BigDecimal.TEN,
            modelIds.get(1), BigDecimal.valueOf(50),
            modelIds.get(2), BigDecimal.valueOf(40));

    RegionModelDistribution distribution =
        createDistribution(
            null, TEST_REGION_ID, now.minusDays(1), now.plusDays(1), initialModelPercentageMap);

    // Execute initial creation
    Response<Void> response = executeCreateOrUpdateRequest(distribution);
    assertSuccessfulResponse(response);

    // Get id
    Response<GetRegionModelDistributionResponse> getResponse =
        dynamicPricingServiceApi.getRegionModelDistribution(TEST_REGION_ID).execute();
    assertTrue(getResponse.isSuccessful());
    assertNotNull(getResponse.body());
    Long id = getResponse.body().getModelDistributionVersions().get(0).getId();

    // Prepare update data
    OffsetDateTime effectiveFrom = now.minusDays(10);
    OffsetDateTime effectiveTo = now.plusDays(15);

    Map<Long, BigDecimal> updatedModelPercentageMap =
        Map.of(
            modelIds.get(0), BigDecimal.valueOf(50),
            modelIds.get(1), BigDecimal.valueOf(50));

    RegionModelDistribution updatedDistribution =
        createDistribution(
            id, TEST_REGION_ID, effectiveFrom, effectiveTo, updatedModelPercentageMap);

    // Execute update
    response = executeCreateOrUpdateRequest(updatedDistribution);
    assertSuccessfulResponse(response);

    // Verify update was successful
    getResponse = dynamicPricingServiceApi.getRegionModelDistribution(TEST_REGION_ID).execute();

    assertTrue(getResponse.isSuccessful());
    GetRegionModelDistributionResponse responseBody = getResponse.body();
    assertNotNull(responseBody);
    assertEquals(TEST_REGION_ID, responseBody.getRegionId());
    assertEquals(1, responseBody.getModelDistributionVersions().size());
    assertEquals(3, responseBody.getModelDistributionVersions().get(0).getModels().size());
    assertEquals(
        modelIds.get(0),
        responseBody.getModelDistributionVersions().get(0).getModels().get(0).getModelId());
    assertEquals(
        BigDecimal.valueOf(50),
        responseBody.getModelDistributionVersions().get(0).getModels().get(0).getPercentage());
  }

  /** Tests error handling when attempting to create a distribution with overlapping date ranges. */
  @Test
  public void testCreateRegionModelDistribution_WithOverlappingDateRanges() throws IOException {
    // First create a region model distribution
    List<Long> modelIds = setupModels();
    OffsetDateTime now = OffsetDateTime.now();

    Map<Long, BigDecimal> initialModelPercentageMap =
        Map.of(
            modelIds.get(0), BigDecimal.TEN,
            modelIds.get(1), BigDecimal.valueOf(50),
            modelIds.get(2), BigDecimal.valueOf(40));

    RegionModelDistribution distribution =
        createDistribution(
            null, TEST_REGION_ID, now.minusDays(1), now.plusDays(1), initialModelPercentageMap);

    // Execute initial creation
    Response<Void> response = executeCreateOrUpdateRequest(distribution);
    assertSuccessfulResponse(response);

    // Create a new distribution with overlapping date ranges
    RegionModelDistribution distributionWithOverlap =
        createDistribution(null, TEST_REGION_ID, now, now.plusDays(1), initialModelPercentageMap);

    // Execute initial creation
    Response<Void> responseWithOverlap = executeCreateOrUpdateRequest(distributionWithOverlap);

    // Assert
    assertFalse(responseWithOverlap.isSuccessful());
    assertEquals(400, responseWithOverlap.code());
    Assertions.assertNotNull(responseWithOverlap.errorBody());
    Assertions.assertTrue(
        responseWithOverlap
            .errorBody()
            .string()
            .contains(SURGE_COMPUTATION_MODEL_OVERLAPPING_DATES_ERROR.getMessage()));
  }

  /** Tests error handling when attempting to create a distribution with non-existent models. */
  @Test
  public void testCreateRegionModelDistribution_WithNotExistingModel() throws IOException {
    // Arrange - create distribution with not existing model
    OffsetDateTime now = OffsetDateTime.now();

    Map<Long, BigDecimal> modelPercentageMap =
        Map.of(998L, BigDecimal.TEN, 999L, BigDecimal.valueOf(90));

    RegionModelDistribution distribution =
        createDistribution(
            null, TEST_REGION_ID, now.minusDays(1), now.plusDays(1), modelPercentageMap);

    // Execute
    Response<Void> response = executeCreateOrUpdateRequest(distribution);

    // Assert
    assertFalse(response.isSuccessful());
    assertEquals(400, response.code());
    Assertions.assertNotNull(response.errorBody());
    Assertions.assertTrue(
        response.errorBody().string().contains(NOT_FOUND_SURGE_COMPUTATION_MODEL.getMessage()));
  }

  /** Tests validation when model percentages don't sum to 100%. */
  @Test
  public void testCreateRegionModelDistribution_InvalidPercentage() throws IOException {
    // Arrange - create distribution with percentage sum not equal to 100
    List<Long> modelIds = setupModels();
    OffsetDateTime now = OffsetDateTime.now();

    Map<Long, BigDecimal> modelPercentageMap =
        Map.of(
            modelIds.get(0), BigDecimal.TEN,
            modelIds.get(1), BigDecimal.TEN);

    RegionModelDistribution distribution =
        createDistribution(
            null, TEST_REGION_ID, now.minusDays(1), now.plusDays(1), modelPercentageMap);

    // Execute
    Response<Void> response = executeCreateOrUpdateRequest(distribution);

    // Assert
    assertFalse(response.isSuccessful());
    assertEquals(400, response.code());
    Assertions.assertNotNull(response.errorBody());
    Assertions.assertTrue(
        response
            .errorBody()
            .string()
            .contains(
                MessageFormat.format(SURGE_COMPUTATION_MODEL_PERCENTAGE_ERROR.getMessage(), 20)));
  }

  /** Tests validation of required fields. */
  @Test
  public void testCreateRegionModelDistribution_MissingRequiredFields() throws IOException {
    // Arrange - missing region ID
    RegionModelDistribution distribution = new RegionModelDistribution();
    // regionId not set

    // Execute
    Response<Void> response = executeCreateOrUpdateRequest(distribution);

    // Assert
    assertFalse(response.isSuccessful());
    assertEquals(400, response.code());
  }

  @Test
  public void testCreateRegionModelDistribution_whenPreviousVersionEffectiveToIsNull()
      throws IOException {
    // Arrange
    List<Long> modelIds = setupModels();
    OffsetDateTime now = OffsetDateTime.now();

    Map<Long, BigDecimal> modelPercentageMap =
        Map.of(
            modelIds.get(1), BigDecimal.valueOf(60),
            modelIds.get(2), BigDecimal.valueOf(40));

    RegionModelDistribution distribution =
        createDistribution(null, TEST_REGION_ID, now.minusDays(10), null, modelPercentageMap);

    // Execute
    Response<Void> response = executeCreateOrUpdateRequest(distribution);

    // Assert
    assertSuccessfulResponse(response);

    distribution =
        createDistribution(null, TEST_REGION_ID, now.plusDays(1), null, modelPercentageMap);
    // Execute
    response = executeCreateOrUpdateRequest(distribution);
    // Assert
    assertSuccessfulResponse(response);

    // Verify creation was successful
    Response<GetRegionModelDistributionResponse> getResponse =
        dynamicPricingServiceApi.getRegionModelDistribution(TEST_REGION_ID).execute();

    assertTrue(getResponse.isSuccessful());
    GetRegionModelDistributionResponse responseBody = getResponse.body();
    assertNotNull(responseBody);
    assertEquals(TEST_REGION_ID, responseBody.getRegionId());
    assertEquals(2, responseBody.getModelDistributionVersions().size());
    assertNull(responseBody.getModelDistributionVersions().get(0).getEffectiveTo());
    assertNotNull(responseBody.getModelDistributionVersions().get(1).getEffectiveTo());
  }

  /**
   * Creates a region model distribution object with the specified parameters.
   *
   * @param regionId Region ID
   * @param effectiveFrom Start date of effectiveness
   * @param effectiveTo End date of effectiveness
   * @param modelPercentages Map of model IDs to their percentage values
   * @return Configured RegionModelDistribution object
   */
  private RegionModelDistribution createDistribution(
      Long id,
      Long regionId,
      OffsetDateTime effectiveFrom,
      OffsetDateTime effectiveTo,
      Map<Long, BigDecimal> modelPercentages) {

    RegionModelDistribution distribution = new RegionModelDistribution();
    distribution.setId(id);
    distribution.setRegionId(regionId);
    distribution.setEffectiveFrom(effectiveFrom);
    distribution.setEffectiveTo(effectiveTo);

    List<RegionModelDistributionModelsInner> modelsList =
        modelPercentages.entrySet().stream()
            .map(
                entry ->
                    new RegionModelDistributionModelsInner()
                        .modelId(entry.getKey())
                        .percentage(entry.getValue()))
            .collect(Collectors.toList());

    distribution.setModels(modelsList);
    return distribution;
  }

  /**
   * Executes the API request to create or update a region model distribution.
   *
   * @param distribution The distribution to create or update
   * @return API response
   */
  private Response<Void> executeCreateOrUpdateRequest(RegionModelDistribution distribution)
      throws IOException {
    return dynamicPricingServiceApi
        .createOrUpdateRegionModelDistribution(distribution, USER_ID)
        .execute();
  }

  /**
   * Verifies that the API response indicates success.
   *
   * @param response The API response to verify
   */
  private void assertSuccessfulResponse(Response<Void> response) {
    assertTrue(response.isSuccessful());
    assertEquals(204, response.code());
  }

  /**
   * Sets up test surge computation models.
   *
   * @return List of created model IDs
   */
  private List<Long> setupModels() throws IOException {
    List<Long> modelIds = new ArrayList<>();

    // Create three test models
    String[][] modelData = {
      {
        "Test Surge Model1",
        "Test description for surge model1",
        "http://test-endpoint.com/api/surge1"
      },
      {
        "Test Surge Model2",
        "Test description for surge model2",
        "http://test-endpoint.com/api/surge2"
      },
      {
        "Test Surge Model3",
        "Test description for surge model3",
        "http://test-endpoint.com/api/surge3"
      }
    };

    for (String[] data : modelData) {
      SurgeComputationModelRequest request = new SurgeComputationModelRequest();
      request.setModelName(data[0]);
      request.setDescription(data[1]);
      request.setEndpointUrl(data[2]);

      Response<SurgeComputationModelResponse> response =
          dynamicPricingServiceApi.createSurgeComputationModel(request, USER_ID).execute();

      assertTrue(response.isSuccessful());
      assertNotNull(response.body());
      assertNotNull(response.body().getData());
      modelIds.add(response.body().getData().getId());
    }

    return modelIds;
  }
}
